import { Col, Empty, Row, Typography } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { Loading, SearchBar } from '~/components';
import { useAppAuth } from '~/hooks';
import { Service, getAvailableServices, searchServices } from '~/services';
import { createStyles } from '~/themes';
import ServiceCard from './ServiceCard';

const { Title, Text } = Typography;

interface ServicesGridProps {
  className?: string;
}

const ServicesGrid: React.FC<ServicesGridProps> = ({ className }) => {
  const { styles } = useStyles();
  const { isAuthenticated, user } = useAppAuth();

  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [availableServices, setAvailableServices] = useState<Service[]>([]);

  // Simuler le chargement des services
  useEffect(() => {
    const loadServices = async () => {
      setLoading(true);
      try {
        // Simuler un délai de chargement
        await new Promise(resolve => setTimeout(resolve, 500));

        const userRole = user?.role || '';
        const services = getAvailableServices(isAuthenticated, userRole);
        setAvailableServices(services);
      } catch (error) {
        console.error('Erreur lors du chargement des services:', error);
      } finally {
        setLoading(false);
      }
    };

    loadServices();
  }, [isAuthenticated, user]);

  // Filtrer les services selon la recherche
  const filteredServices = useMemo(() => {
    return searchServices(searchQuery, availableServices);
  }, [searchQuery, availableServices]);

  const getGridProps = () => {
    const serviceCount = filteredServices.length;
    if (serviceCount <= 2) return { xs: 24, sm: 12, md: 12, lg: 8, xl: 6 };
    if (serviceCount <= 4) return { xs: 24, sm: 12, md: 8, lg: 6, xl: 6 };
    return { xs: 24, sm: 12, md: 8, lg: 6, xl: 4 };
  };

  if (loading) {
    return (
      <div className={`${styles.container} ${className || ''}`}>
        <div className={styles.loadingContainer}>
          <Loading noCss />
          <Text className={styles.loadingText}>Chargement des services...</Text>
        </div>
      </div>
    );
  }

  return (
    <div className={`${styles.container} ${className || ''}`}>
      <div className={styles.header}>
        {/* <Title level={2} className={styles.title}>
          Services Disponibles
        </Title> */}
        <Text className={styles.subtitle}>
          {availableServices.length === 0 ?
            "Aucun service disponible" :
            availableServices.length === 1 ?
            `Service ${!isAuthenticated ?'public disponible': 'auquel vous avez accès'} (1 service)` :
            `Services ${!isAuthenticated ?'publics disponibles': 'auxquels vous avez accès'} (${availableServices.length} services)`}
        </Text>
      </div>

      <SearchBar
        value={searchQuery}
        onChange={setSearchQuery}
        placeholder="Rechercher un service par nom, description ou mot-clé..."
        className={styles.searchBar}
      />

      {filteredServices.length === 0 ? (
        <Empty
          className={styles.emptyState}
          description={
            searchQuery
              ? `Aucun service trouvé pour "${searchQuery}"`
              : "Aucun service disponible"
          }
        />
      ) : (
        <>
          {searchQuery && (
            <div className={styles.searchResults}>
              <Text className={styles.resultsText}>
                {filteredServices.length} service{filteredServices.length > 1 ? 's' : ''} trouvé{filteredServices.length > 1 ? 's' : ''} pour "{searchQuery}"
              </Text>
            </div>
          )}

          <Row gutter={[24, 24]} className={styles.servicesGrid}>
            {filteredServices.map((service, index) => (
              <Col key={index} {...getGridProps()}>
                <ServiceCard key={service.id} service={service} />
              </Col>
            ))}
          </Row>
        </>
      )}
    </div>
  );
};

const useStyles = createStyles(({ token }) => ({
  container: {
    padding: '2rem',
    maxWidth: '1400px',
    margin: '0 auto',
  },
  header: {
    textAlign: 'center',
    marginBottom: '2rem',
  },
  title: {
    color: token.colorText,
    marginBottom: '0.5rem !important',
    fontSize: '2.5rem !important',
    fontWeight: '700 !important',
  },
  subtitle: {
    color: token.colorTextSecondary,
    fontSize: '16px',
    display: 'block',
  },
  searchBar: {
    marginBottom: '2rem',
  },
  loadingContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '4rem 0',
    gap: '1rem',
  },
  loadingText: {
    color: token.colorTextSecondary,
    fontSize: '16px',
  },
  emptyState: {
    padding: '4rem 0',
    '& .ant-empty-description': {
      color: token.colorTextSecondary,
      fontSize: '16px',
    },
  },
  searchResults: {
    marginBottom: '1.5rem',
    padding: '0.75rem 1rem',
    backgroundColor: token.colorBgContainer,
    borderRadius: '8px',
    border: `1px solid ${token.colorBorder}`,
  },
  resultsText: {
    color: token.colorTextSecondary,
    fontSize: '14px',
    fontWeight: 500,
  },
  servicesGrid: {
    marginTop: '1rem',
  },
}));

export default ServicesGrid;
