import React from 'react';
import { ProCard, ProCardProps } from '@ant-design/pro-components';
import { Card, Space, Typography, theme } from 'antd';
import { createStyles } from 'antd-style';
import { CSSProperties, ReactNode } from 'react';

const { Title, Text } = Typography;

// Styles personnalisés pour AppCard
const useStyles = createStyles(({ token, css }) => ({
  appCard: css`
    // .dgd-pro-card-header {
    //   background: linear-gradient(135deg, ${token.colorPrimary}15 0%, ${token.colorPrimary}08 100%);
    //   border-bottom: 1px solid ${token.colorBorder};
    //   border-radius: ${token.borderRadius}px ${token.borderRadius}px 0 0;
    //   padding: ${token.paddingMD}px ${token.paddingLG}px;
    // }

    .dgd-pro-card-body {
      padding: ${token.paddingLG}px;
    }

    &.app-card-elevated {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
      }
    }

    &.app-card-gradient {
      background: linear-gradient(135deg, ${token.colorBgContainer} 0%, ${token.colorFillQuaternary} 100%);
    }

    &.app-card-glass {
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
  `,

  cardHeader: css`
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0;
  `,

  cardTitle: css`
    display: flex;
    align-items: center;
    gap: ${token.marginSM}px;
    margin: 0;

    .anticon {
      color: ${token.colorPrimary};
      font-size: ${token.fontSizeLG}px;
    }
  `,

  cardExtra: css`
    display: flex;
    align-items: center;
    gap: ${token.marginSM}px;
  `,

  cardDescription: css`
    color: ${token.colorTextSecondary};
    margin-top: ${token.marginXS}px;
    font-size: ${token.fontSizeSM}px;
  `
}));

export interface AppCardProps extends Omit<ProCardProps, 'title'> {
  /** Titre de la carte */
  title?: ReactNode;
  /** Description sous le titre */
  description?: ReactNode;
  /** Icône à afficher à côté du titre */
  icon?: ReactNode;
  /** Éléments supplémentaires dans le header */
  extra?: ReactNode;
  /** Variante de style */
  variant?: 'default' | 'elevated' | 'gradient' | 'glass';
  /** Taille de la carte */
  size?: 'small' | 'default' | 'large';
  /** Style personnalisé pour le header */
  headerStyle?: CSSProperties;
  /** Style personnalisé pour le body */
  bodyStyle?: CSSProperties;
  /** Afficher ou non la bordure du header */
  headerBordered?: boolean;
  /** Couleur d'accent pour le header */
  accentColor?: string;
}

const AppCard: React.FC<AppCardProps> = ({
  title,
  description,
  icon,
  extra,
  variant = 'default',
  size = 'default',
  headerStyle,
  bodyStyle,
  headerBordered = true,
  accentColor,
  className,
  children,
  ...props
}) => {
  const { styles, cx } = useStyles();
  const { token } = theme.useToken();

  // Calcul des classes CSS
  const cardClasses = cx(
    styles.appCard,
    {
      'app-card-elevated': variant === 'elevated',
      'app-card-gradient': variant === 'gradient',
      'app-card-glass': variant === 'glass',
    },
    className
  );

  // Calcul des tailles
  const sizeConfig = {
    small: { padding: token.paddingSM, titleLevel: 5 as const },
    default: { padding: token.paddingMD, titleLevel: 4 as const },
    large: { padding: token.paddingLG, titleLevel: 3 as const }
  };

  const currentSize = sizeConfig[size];

  // Rendu du header personnalisé
  const renderHeader = () => {
    if (!title && !extra) return undefined;

    return (
      <div
        className={styles.cardHeader}
        style={{
          borderLeft: accentColor ? `4px solid ${accentColor}` : undefined,
          paddingLeft: accentColor ? token.paddingMD : undefined,
          ...headerStyle
        }}
      >
        <div>
          {title && (
            <Title
              level={currentSize.titleLevel}
              className={styles.cardTitle}
            >
              {icon && <span>{icon}</span>}
              {title}
            </Title>
          )}
          {description && (
            <Text className={styles.cardDescription}>
              {description}
            </Text>
          )}
        </div>

        {extra && (
          <div className={styles.cardExtra}>
            {extra}
          </div>
        )}
      </div>
    );
  };

  return (
    <ProCard
      {...props}
      className={cardClasses}
      title={renderHeader()}
      headerBordered
      // headerBordered={headerBordered && (title || extra)}
      bodyStyle={{
        padding: currentSize.padding,
        ...bodyStyle
      }}
      size={size}
    >
      {children}
    </ProCard>
  );
};

export default AppCard;
