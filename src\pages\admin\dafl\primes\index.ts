import { PATH_ADMIN, PROXY1 } from "~/constants";
import { AppResponse } from "~/models";
import AppError from "~/services/AppError";
import request from "~/services/request";
import { createPath } from "~/services/utils";
import { createStyles } from "~/themes";

// ========= CONSTANTS
export const PRIMES = "primes"
export const REPARTITIONS = "repartitions"
// export const ADD = "add"
// export const MODIFY = "modify"
// export const COEFFICIENTS = "coefficients"
// export const MANAGEMENT = "management"
// export const TYPE_PRIME = "type-prime"
// export const REPORTS = "reports"
// export const SETTINGS = "settings"

export const PRIMES_PATH = createPath(PRIMES, PATH_ADMIN.dafl)
export const REPARTITIONS_PATH = createPath(REPARTITIONS, PRIMES_PATH)

// ========== MODELS
export interface Prime {
  id: string;
  name: string;
  // type: PrimeType;
  // amount: number;
  // date: string;
  // status: PrimeStatus;
  // fileName?: string;
  // periodicite?: Periodicite;
  // avec_unite?: boolean;
  // description?: string;
  // periode?: string;
  // unites?: UniteVersement[];
}

// ========== API
export const getRepartitionsListApi = (): Promise<any> => {
  return request.get("/prime/repartition/repartitions", { devPrefix: PROXY1 });
};

// Service pour récupérer les types de prime
export const getTypesPrimesApi = (): Promise<AppResponse<any>> => {
  return request.get<AppResponse<any>>(`/prime/typeprime/primess`, { devPrefix: PROXY1 });
};

// Service pour récupérer les fonctions
export const getFonctionsApi = (): Promise<AppResponse<any>> => {
  return request.get<AppResponse<any>>(`/prime/fonctions/all`, { devPrefix: PROXY1 });
};

export const createRepartitionApi = (data: any): Promise<any> => {
  return request.post("/prime/repartition/createRepartition", { data, devPrefix: PROXY1, timeout: -1 });
};


// ========== LOADERS
export const repartitionsListLoader = async (): Promise<any> => {
  try {
    const response = await getRepartitionsListApi();
    console.log("RepartitionsListLoader response", response);
    return response;
  } catch (error) {
    return AppError.message(error, "Erreur lors du chargement de repartitionsListLoader", []);
  }
};
export const repartitionsNewLoader = async (): Promise<any> => {
  try {
    // const [primeTypesResponse, fonctionsResponse, agentsResponse] = await Promise.all([
    //   getTypesPrimesApi(),
    //   getFonctionsApi(),
    //   Promise.resolve({ data: [] })
    // ]);
    // console.log("====================== primeTypesResponse ===============================")
    // console.log(primeTypesResponse);
    // console.log("====================== fonctionsResponse ===============================")
    // console.log(fonctionsResponse);
    // console.log("====================== agentsResponse ===============================")
    // console.log(agentsResponse);
    // const response = {
    //   primeTypes: primeTypesResponse.data,
    //   fonctions: fonctionsResponse.data,
    //   agents: agentsResponse.data
    // };
    const response = await getTypesPrimesApi()
    return response.data;
  } catch (error) {
    return AppError.message(error, "Erreur lors du chargement de repartitionsNewLoader", []);
  }
};

// ========== STYLES
export const useStyles = createStyles(({ token }) => ({
  gridStyle: {
    width: '25%',
    textAlign: 'center',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    '&:hover': {
      backgroundColor: token.colorPrimaryBg,
      borderColor: token.colorPrimary,
      transform: 'translateY(-2px)',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    },
    '&:active': {
      transform: 'translateY(0)',
    },
  },
  gridContent: {
    padding: '0.1rem',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '6px',
  },
  gridIcon: {
    fontSize: '32px',
    color: token.colorPrimary,
  },
  gridTitle: {
    fontSize: '16px',
    fontWeight: 600,
    color: token.colorText,
    margin: 0,
  },
  gridDescription: {
    fontSize: '14px',
    color: token.colorTextSecondary,
    margin: 0,
    textAlign: 'center',
  },
}));
