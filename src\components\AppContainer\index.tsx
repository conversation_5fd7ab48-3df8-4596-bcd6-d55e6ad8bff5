import React from 'react';
import { <PERSON><PERSON>ontainer, PageContainerProps } from '@ant-design/pro-components';
import { Button, Space, Typography, Breadcrumb, theme, Affix, Tooltip } from 'antd';
import { createStyles } from 'antd-style';
import { CSSProperties, ReactNode } from 'react';
import {
  HomeOutlined,
  SettingOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  LeftOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

// Styles personnalisés pour AppContainer
const useStyles = createStyles(({ token, css }) => ({
  appContainer: css`
    min-height: calc(100vh - 64px);
    background: linear-gradient(135deg, ${token.colorBgLayout} 0%, ${token.colorBgContainer} 100%);

    // .dgd-page-header {
    //   background: rgba(255, 255, 255, 0.8);
    //   backdrop-filter: blur(10px);
    //   border-bottom: 1px solid ${token.colorBorder};
    //   margin-bottom: ${token.marginLG}px;
    //   border-radius: ${token.borderRadius}px ${token.borderRadius}px 0 0;
    //   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    // }

    &.app-container-fullscreen {
      .dgd-page-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        border-radius: 0;
      }

      .dgd-pro-page-container-children-content {
        padding-top: 80px;
      }
    }

    &.app-container-glass {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);

      .dgd-page-header {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(15px);
      }
    }

    &.app-container-gradient {
      background: linear-gradient(135deg,
        ${token.colorPrimary}08 0%,
        ${token.colorSuccess}08 50%,
        ${token.colorInfo}08 100%);
    }

    &.app-container-minimal {
      .dgd-page-header {
        background: transparent;
        border-bottom: none;
        box-shadow: none;
        backdrop-filter: none;
      }
    }
  `,

  containerHeader: css`
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: ${token.paddingLG}px;
  `,

  headerLeft: css`
    display: flex;
    align-items: center;
    gap: ${token.marginXL}px;
  `,

  headerTitle: css`
    display: flex;
    align-items: center;
    gap: ${token.marginSM}px;
    margin: 0;

    .anticon {
      color: ${token.colorPrimary};
      font-size: ${token.fontSizeXL}px;
    }
  `,

  headerSubtitle: css`
    color: ${token.colorTextSecondary};
    margin-top: ${token.marginXS}px;
    font-size: ${token.fontSizeSM}px;
  `,

  headerActions: css`
    display: flex;
    align-items: center;
    gap: ${token.marginLG}px;
    margin-left: ${token.marginXL}px;
  `,

  backButton: css`
    display: flex;
    align-items: center;
    gap: ${token.marginXS}px;
    color: ${token.colorText};
    transition: all 0.2s ease;

    &:hover {
      color: ${token.colorPrimary};
      transform: translateX(-2px);
    }
  `,

  breadcrumbContainer: css`
    margin-bottom: ${token.marginMD}px;

    .dgd-breadcrumb {
      font-size: ${token.fontSizeSM}px;
    }
  `,

  floatingActions: css`
    position: fixed;
    bottom: ${token.marginXL}px;
    right: ${token.marginXL}px;
    z-index: 999;

    .dgd-btn {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      border: none;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
      }
    }
  `
}));

export interface AppContainerProps extends Omit<PageContainerProps, 'title'> {
  /** Titre de la page */
  title?: ReactNode;
  /** Sous-titre ou description */
  subtitle?: ReactNode;
  /** Icône pour le titre */
  icon?: ReactNode;
  /** Variante de style */
  variant?: 'default' | 'glass' | 'gradient' | 'minimal' | 'fullscreen';
  /** Afficher le bouton retour */
  showBackButton?: boolean;
  /** Texte du bouton retour */
  backButtonText?: string;
  /** Callback du bouton retour */
  onBack?: () => void;
  /** Breadcrumb personnalisé */
  breadcrumbItems?: Array<{
    title: ReactNode;
    href?: string;
    onClick?: () => void;
  }>;
  /** Actions flottantes */
  floatingActions?: ReactNode[];
  /** Actions dans le header */
  headerActions?: ReactNode[];
  /** Couleur d'accent */
  accentColor?: string;
  /** Afficher en plein écran */
  fullscreen?: boolean;
  /** Callback pour basculer le plein écran */
  onFullscreenToggle?: (fullscreen: boolean) => void;
}

const AppContainer: React.FC<AppContainerProps> = ({
  title,
  subtitle,
  icon,
  variant = 'default',
  showBackButton = false,
  backButtonText = 'Retour',
  onBack,
  breadcrumbItems,
  floatingActions,
  headerActions = [],
  accentColor,
  fullscreen = false,
  onFullscreenToggle,
  className,
  children,
  ...props
}) => {
  const { styles, cx } = useStyles();
  const { token } = theme.useToken();

  // Classes CSS
  const containerClasses = cx(
    styles.appContainer,
    {
      'app-container-glass': variant === 'glass',
      'app-container-gradient': variant === 'gradient',
      'app-container-minimal': variant === 'minimal',
      'app-container-fullscreen': variant === 'fullscreen' || fullscreen,
    },
    className
  );

  // Rendu du header personnalisé
  const renderHeader = () => {
    if (!title && !showBackButton && !headerActions.length) return undefined;

    return {
      title: (
        <div className={styles.containerHeader}>
          <div className={styles.headerLeft}>
            {showBackButton && (
              <Tooltip title={backButtonText}>
                <Button
                  type="link"
                  shape='circle'
                  icon={<LeftOutlined />}
                  onClick={onBack}
                  className={styles.backButton}
                />
              </Tooltip>
            )}

            <div>
              {title && (
                <Title
                  level={2}
                  className={styles.headerTitle}
                  style={{
                    borderLeft: accentColor ? `4px solid ${accentColor}` : undefined,
                    paddingLeft: accentColor ? token.paddingMD : undefined,
                  }}
                >
                  {icon && <span>{icon}</span>}
                  {title}
                </Title>
              )}
              {subtitle && (
                <Text className={styles.headerSubtitle}>
                  {subtitle}
                </Text>
              )}
            </div>
          </div>

          <div className={styles.headerActions}>
            <Space>
              {headerActions}
              {onFullscreenToggle && (
                <Button
                  type="text"
                  icon={fullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                  onClick={() => onFullscreenToggle(!fullscreen)}
                  title={fullscreen ? 'Quitter le plein écran' : 'Plein écran'}
                />
              )}
              <Button
                type="text"
                icon={<SettingOutlined />}
                title="Paramètres"
              />
            </Space>
          </div>
        </div>
      )
    };
  };

  // Rendu du breadcrumb
  const renderBreadcrumb = () => {
    if (!breadcrumbItems?.length) return undefined;

    return (
      <div className={styles.breadcrumbContainer}>
        <Breadcrumb>
          <Breadcrumb.Item href="/">
            <HomeOutlined />
          </Breadcrumb.Item>
          {breadcrumbItems.map((item, index) => (
            <Breadcrumb.Item
              key={index}
              href={item.href}
              onClick={item.onClick}
            >
              {item.title}
            </Breadcrumb.Item>
          ))}
        </Breadcrumb>
      </div>
    );
  };

  return (
    <>
      <PageContainer
        {...props}
        className={containerClasses}
        header={renderHeader()}
        content={renderBreadcrumb()}
      >
        {children}
      </PageContainer>

      {/* Actions flottantes */}
      {floatingActions && floatingActions.length > 0 && (
        <Affix offsetBottom={24}>
          <div className={styles.floatingActions}>
            <Space direction="vertical">
              {floatingActions}
            </Space>
          </div>
        </Affix>
      )}
    </>
  );
};

export default AppContainer;
