import { Suspense, useEffect } from "react"
import { useLocation } from "react-router"
import { Loading } from "~/components"
import { useAppAuth } from "~/hooks"
import AppError from "~/services/AppError"

// Create an HOC to wrap your route components with ScrollToTop
export const wrapPage = (Component: any, props: any = {}, withProtectPage?: boolean) => {
  return () => {
    const { pathname } = useLocation()
    const { user } = useAppAuth();

    useEffect(() => {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: "smooth",
      }) // Scroll to the top when the location changes
    }, [pathname])

    if (withProtectPage && user?.role !== 'admin') {
      const {requiredRoles, revokedRoles, requiredPermissions, revokedPermissions}:
          {requiredRoles: string[], revokedRoles: string[], requiredPermissions: string[], revokedPermissions: string[]}
        = props;

      // Si aucun rôle ou permission n'est spécifié, on bloque l'accès
      if (!requiredRoles && !revokedRoles && !requiredPermissions && !revokedPermissions)
        throw new AppError("Accès non autorisé",undefined, 403);

      // Vérifier les rôles si nécessaire
      if (requiredRoles && requiredRoles.length > 0) {
        const hasRequiredRole = requiredRoles.some((role:string) => role.toUpperCase() === (user?.role || ''));
        if (!hasRequiredRole) {
          // Rediriger vers une page d'erreur 403 si l'utilisateur n'a pas les droits
          throw new AppError("Accès non autorisé",undefined, 403);
        }
      }

      // Vérifier les rôles interdits si nécessaire
      if (revokedRoles && revokedRoles.length > 0) {
        const hasRevokedRole = revokedRoles.some((role:string) => role.toUpperCase() === (user?.role || ''));
        if (hasRevokedRole) {
          // Rediriger vers une page d'erreur 403 si l'utilisateur n'a pas les droits
          throw new AppError("Accès non autorisé",undefined, 403);
        }
      }

      // Vérifier les permissions si nécessaire
      if (requiredPermissions && requiredPermissions.length > 0) {
        const hasRequiredPermission = requiredPermissions.some((permission:string) => user?.permissions?.includes(permission) || false);
        if (!hasRequiredPermission) {
          // Rediriger vers une page d'erreur 403 si l'utilisateur n'a pas les droits
          throw new AppError("Accès non autorisé",undefined, 403);
        }
      }

      // Vérifier les permissions interdites si nécessaire
      if (revokedPermissions && revokedPermissions.length > 0) {
        const hasRevokedPermission = revokedPermissions.some((permission:string) => user?.permissions?.includes(permission) || false);
        if (hasRevokedPermission) {
          // Rediriger vers une page d'erreur 403 si l'utilisateur n'a pas les droits
          throw new AppError("Accès non autorisé",undefined, 403);
        }
      }

    }

    return (
      <Suspense fallback={<Loading />}>
        <Component {...props} />
      </Suspense>
    )
  }
}
