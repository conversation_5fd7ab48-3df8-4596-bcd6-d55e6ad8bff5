import { useLoaderData, useNavigate } from "react-router";
import { PRIMES_PATH } from "..";
import { PageContainer, ProTable, ProCard, ProColumns } from "@ant-design/pro-components";
import { ArrowLeftOutlined, PlusCircleOutlined, FilterOutlined } from "@ant-design/icons";
import { Button, Select, Space, Typography, Tag, Card, Table } from "antd";
import { useState, useEffect } from "react";

const { Option } = Select;
const { Title } = Typography;

// Interface pour les données de répartition
interface RepartitionData {
  idRepartition: number;
  codeTypePrime: string;
  idPrime: number;
  periode: string;
  annee: string;
}

// Données mockées pour la démonstration
const mockRepartitions: RepartitionData[] = [
  {
    idRepartition: 1,
    codeTypePrime: "PRIME_RENDEMENT",
    idPrime: 101,
    periode: "T1",
    annee: "2024"
  },
  {
    idRepartition: 2,
    codeTypePrime: "PRIME_PERFORMANCE",
    idPrime: 102,
    periode: "T2",
    annee: "2024"
  },
  {
    idRepartition: 3,
    codeTypePrime: "PRIME_RENDEMENT",
    idPrime: 103,
    periode: "T3",
    annee: "2024"
  },
  {
    idRepartition: 4,
    codeTypePrime: "PRIME_EXCEPTIONNELLE",
    idPrime: 104,
    periode: "T4",
    annee: "2024"
  },
  {
    idRepartition: 5,
    codeTypePrime: "PRIME_PERFORMANCE",
    idPrime: 105,
    periode: "T1",
    annee: "2023"
  }
];

// Types de primes disponibles
const typesPrimes = [
  { value: "PRIME_RENDEMENT", label: "Prime de Rendement" },
  { value: "PRIME_PERFORMANCE", label: "Prime de Performance" },
  { value: "PRIME_EXCEPTIONNELLE", label: "Prime Exceptionnelle" },
  { value: "PRIME_ANNUELLE", label: "Prime Annuelle" }
];

const RepartitionsPage = () => {
  const _load = useLoaderData();
  const [loading, setLoading] = useState(false);
  const [filteredData, setFilteredData] = useState<RepartitionData[]>(mockRepartitions);
  const [selectedTypePrime, setSelectedTypePrime] = useState<string | undefined>(undefined);
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(PRIMES_PATH);
  };

  // Filtrage des données par type de prime
  useEffect(() => {
    if (selectedTypePrime) {
      const filtered = mockRepartitions.filter(item => item.codeTypePrime === selectedTypePrime);
      setFilteredData(filtered);
    } else {
      setFilteredData(mockRepartitions);
    }
  }, [selectedTypePrime]);

  // Définition des colonnes pour ProTable
  const columns: ProColumns<RepartitionData>[] = [
    {
      title: "ID Répartition",
      dataIndex: "idRepartition",
      key: "idRepartition",
      width: 120,
      sorter: (a, b) => a.idRepartition - b.idRepartition,
    },
    {
      title: "Type de Prime",
      dataIndex: "codeTypePrime",
      key: "codeTypePrime",
      width: 180,
      render: (text: string) => {
        const type = typesPrimes.find(t => t.value === text);
        const colors = {
          "PRIME_RENDEMENT": "blue",
          "PRIME_PERFORMANCE": "green",
          "PRIME_EXCEPTIONNELLE": "orange",
          "PRIME_ANNUELLE": "purple"
        };
        return (
          <Tag color={colors[text as keyof typeof colors] || "default"}>
            {type?.label || text}
          </Tag>
        );
      },
      filters: typesPrimes.map(type => ({ text: type.label, value: type.value })),
      onFilter: (value, record) => record.codeTypePrime === value,
    },
    {
      title: "ID Prime",
      dataIndex: "idPrime",
      key: "idPrime",
      width: 100,
      sorter: (a, b) => a.idPrime - b.idPrime,
    },
    {
      title: "Période",
      dataIndex: "periode",
      key: "periode",
      width: 100,
      render: (text: string) => (
        <Tag color="cyan">{text}</Tag>
      ),
    },
    {
      title: "Année",
      dataIndex: "annee",
      key: "annee",
      width: 100,
      sorter: (a, b) => a.annee.localeCompare(b.annee),
      render: (text: string) => (
        <Tag color="geekblue">{text}</Tag>
      ),
    },
    {
      title: "Actions",
      valueType: "option",
      width: 150,
      render: (_, _record) => [
        <Button key="view" type="link" size="small">
          Voir
        </Button>,
        <Button key="edit" type="link" size="small">
          Modifier
        </Button>,
        <Button key="delete" type="link" danger size="small">
          Supprimer
        </Button>
      ],
    },
  ];

  return (
    <PageContainer
      loading={loading}
      header={{
        title: "Répartitions des Primes",
        onBack: handleBack,
        backIcon: <ArrowLeftOutlined className="mb-1" />,
        extra: [
          <Button
            key="add-repartition"
            type="primary"
            icon={<PlusCircleOutlined />}
            onClick={() => navigate("add")}
            size="large"
          >
            Ajouter une répartition
          </Button>
        ]
      }}
    >
      <ProCard
        title="Liste des Répartitions"
        headerBordered
      >
        {/* Section de filtrage */}
        <Card
          size="small"
          style={{ marginBottom: 16, backgroundColor: '#fafafa' }}
          title={
            <Space>
              <FilterOutlined />
              <span>Filtrer par type de prime</span>
            </Space>
          }
        >
          <Select
            placeholder="Sélectionner un type de prime"
            style={{ width: 300 }}
            allowClear
            value={selectedTypePrime}
            onChange={setSelectedTypePrime}
          >
            {typesPrimes.map(type => (
              <Option key={type.value} value={type.value}>
                {type.label}
              </Option>
            ))}
          </Select>
        </Card>

        {/* Tableau ProTable */}
        <ProTable<RepartitionData>
          // showHeader={false}

          columns={columns}
          dataSource={filteredData}
          rowKey="idRepartition"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} sur ${total} répartitions`,
          }}
          // bordered={true}
          // summary={() => (
          //   <Table.Summary.Row>
          //     <Table.Summary.Cell index={0} colSpan={4}>
          //       Total
          //     </Table.Summary.Cell>
          //     <Table.Summary.Cell index={1}>
          //       {filteredData.reduce((sum, item) => sum + item.idPrime, 0)}
          //     </Table.Summary.Cell>
          //   </Table.Summary.Row>
          // )}

          // scroll={{ x: 800 }}

          // size="small"

          search={false}
          dateFormatter="string"
          // headerTitle="Liste des Répartitions"
          cardBordered
          cardProps={{ title: "Liste des répartitions 2", bordered: true, headerBordered: true }}
          // tableViewRender={(props, dom) => {
          //   return <Card title= "ddd">{dom}</Card>;
          // }}
          toolbar={{
            search: {
              placeholder: "Rechercher par ID Répartition, ID Prime, Période, Année",
              onSearch: value => {
                alert(value);
              },
            },

            // tabs: {
            //   items: [
            //     {
            //       key: "all",
            //       label: "Toutes les répartitions",
            //     },
            //     {
            //       key: "pending",
            //       label: "En attente",
            //     },
            //     {
            //       key: "approved",
            //       label: "Approuvées",
            //     },
            //     {
            //       key: "rejected",
            //       label: "Rejetées",
            //     },
            //   ],
            // },

            // menu: {
            //   type: "tab",
            //   activeKey: "all",
            //   items: [
            //     {
            //       key: "all",
            //       label: "Toutes les répartitions",
            //     },
            //     {
            //       key: "pending",
            //       label: "En attente",
            //     },
            //     {
            //       key: "approved",
            //       label: "Approuvées",
            //     },
            //     {
            //       key: "rejected",
            //       label: "Rejetées",
            //     },
            //   ],
            // },

            actions: [
              <Button
                key="add-repartition"
                type="primary"
                icon={<PlusCircleOutlined />}
                onClick={() => navigate("add")}
                size="large"
              >
                Ajouter une répartition
              </Button>
            ],
            multipleLine: true,
            // filter: [
            //   <Select
            //     key="type-prime"
            //     placeholder="Sélectionner un type de prime"
            //     style={{ width: 300 }}
            //     allowClear
            //     value={selectedTypePrime}
            //     onChange={setSelectedTypePrime}
            //   >
            //     {typesPrimes.map(type => (
            //       <Option key={type.value} value={type.value}>
            //         {type.label}
            //       </Option>
            //     ))}
            //   </Select>,
            //   <Button
            //     key="refresh"
            //     onClick={() => {
            //       setLoading(true);
            //       setTimeout(() => setLoading(false), 1000);
            //     }}
            //   >
            //     Actualiser
            //   </Button>
            // ],
          }}
          toolBarRender={() => [
            <Button
              key="refresh"
              onClick={() => {
                setLoading(true);
                setTimeout(() => setLoading(false), 1000);
              }}
            >
              Actualiser
            </Button>
          ]}
          options={{
            setting: {
              listsHeight: 400,
            },
            reload: false,
            density: false,
            fullScreen: true,


          }}
        />
      </ProCard>
    </PageContainer>
  );
};

export default RepartitionsPage;
