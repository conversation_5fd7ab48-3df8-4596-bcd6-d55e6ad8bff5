import { useLoaderData, useNavigate } from "react-router";
import { PRIMES_PATH } from "..";
import { PageContainer } from "@ant-design/pro-components";
import { ArrowLeftOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { Button } from "antd";


const RepartitionsPage = () => {
  const load = useLoaderData();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(PRIMES_PATH);
  };


  return (
    <PageContainer
      loading={loading}
      header={{
        title: "Répartitions des Primes",
        onBack: handleBack,
        backIcon: <ArrowLeftOutlined className="mb-1" />,
        extra: [
          <Button
            key="add-repartition"
            color="danger"
            icon={<PlusCircleOutlined />}
            onClick={() => navigate("add")}
            size="large"
          >
            Ajouter une répartition
          </Button>
        ]
      }}
    >
      Table
        dataSource={repartitionAgents}
        columns={agentsColumns}
        rowKey="id"
        pagination={{ pageSize: 10, showSizeChanger: true }}
        scroll={{ x: 800 }}
        size="small"
      />
    </PageContainer>
  );
};

export default RepartitionsPage;
