
import { getTypesPrimesApi } from '../services/api/primeService';
import { TypePrime } from '../models/Prime';
import AppError from '~/services/AppError';

// Loader pour les types de primes
export const typesPrimesLoader = async (): Promise<TypePrime[]> => {
  try {
    const response = await getTypesPrimesApi();
    return response.data as TypePrime[];
  } catch (error) {
    return AppError.message(error, "Erreur lors du chargement des types de primes") || [];
  }
};
