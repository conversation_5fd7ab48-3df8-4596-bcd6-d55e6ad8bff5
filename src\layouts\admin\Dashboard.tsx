import {
  AutoComplete,
  AutoCompleteProps,
  Avatar,
  Button,
  Card,
  Dropdown,
  Flex,
  FloatButton,
  Image,
  Input,
  Layout,
  MenuProps,
  message,
  Space,
  Switch,
  Tooltip,
  Typography,
} from "antd"
import { ReactNode, useCallback, useEffect, useRef, useState } from "react"
import { Link, Outlet, useLocation, useNavigate } from "react-router"
import { useResponsive } from "antd-style"
import { useTranslation } from "react-i18next"
import Icon, {
  AppstoreOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  MessageOutlined,
  MoonOutlined,
  QuestionOutlined,
  SettingOutlined,
  SunOutlined,
  UserOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
} from "@ant-design/icons"
import { useAppAuth, useAppLocal, useAppTheme } from "~/hooks"
import { Logo, NProgress } from "~/components"
import { CSSTransition, SwitchTransition, TransitionGroup } from "react-transition-group"
import TagsView from "./tagView"
import { createStyles, cx } from "~/themes"
import { AppDispatch, RootState } from "~/redux/store"
import { useDispatch, useSelector } from "react-redux"
import { menuItems } from "~/menus"
import { themeIcon } from "~/services/utils"
import { MenuDataItem, ProLayout, ProSettings } from "@ant-design/pro-components"
import { HOME, PATH_ADMIN, PATH_GUEST } from "~/constants"
import EnUSSvg from "~/assets/en_US.svg?url"
import FrFRSvg from "~/assets/fr_FR.svg"
import { addMenu, addTag } from "~/redux/slices/tagSlice"
import { Menus } from "~/models"


function Dashboard({ children }: Readonly<{ children: ReactNode }>) {
  const { mobile, tablet } = useResponsive()
  const { updateTheme, theme } = useAppTheme()
  const { t } = useTranslation()
  const navigate = useNavigate()
  const location = useLocation()
  const { tags, activeTagId, menus } = useSelector((state: RootState) => state.tag)
  const nodeRef = useRef(null)
  const nodesRef = useRef({ navigate })
  const [options, setOptions] = useState<AutoCompleteProps["options"]>([])
  const { local, setLocal } = useAppLocal()
  const { hasAnyRoles, hasAnyPermissions, user, logout } = useAppAuth()
  const dispatch = useDispatch<AppDispatch>()
  const [sidebarVisible, setSidebarVisible] = useState(true)
  const [sidebarMenu, setSidebarMenu] = useState<Menus[]>()
  const [isLoading, setIsLoading] = useState(false)
  const ref = useRef({hasAnyRoles, hasAnyPermissions, dispatch})

  const searchResult = useCallback(
    (query: string): AutoCompleteProps["options"] => {
      return menus?.filter(({ path, hideInMenu, labels }: Menus) =>
          !hideInMenu && !!path && query?.trim() && labels[local as keyof typeof labels].trim().toLowerCase().includes(query.trim().toLowerCase()))
        .map(({ labels, path, icon, hideInMenu }: Menus) => ({
          value: labels[local as keyof typeof labels],
          label: (
            <Link to={path as string}>
              <Typography.Paragraph className="my-4">
                {icon}
                <Typography.Text className="ml-1 font-bold">{labels[local as keyof typeof labels]}</Typography.Text>
              </Typography.Paragraph>
            </Link>
          ),
        }))
    },
    [menus],
  )


  const handleSearch = (value: string) => {
    setOptions(value ? searchResult(value) : [])
  }

  const handleLogout = () => {
    message.open({
      type: "loading",
      content: t("Déconnexion en cours"),
    });
    setTimeout(() => {
      logout();
    }, 1000);
  };

  const toggleLocale = () => {
    const newLocale = local === "en" ? "fr" : "en";
    setLocal(newLocale);
  }

  const items: MenuProps["items"] = [
    {
      key: "user-profile-link",
      label: (
        <Flex align="center" gap="small">
          <UserOutlined />
          <span>{user?.email || 'Profile'}</span>
        </Flex>
      ),
    },
    {
      key: "user-settings-link",
      label: (
        <Flex align="center" gap="small">
          <SettingOutlined />
          <span>{t('Settings')}</span>
        </Flex>
      ),
    },
    {
      key: "user-help-link",
      label: (
        <Flex align="center" gap="small">
          <QuestionOutlined />
          <span>{t('Help Center')}</span>
        </Flex>
      ),
    },
    {
      type: "divider",
    },
    {
      key: "user-logout-link",
      label: (
        <Flex align="center" gap="small">
          <LogoutOutlined />
          <span>{t('Logout')}</span>
        </Flex>
      ),
      danger: true,
      onClick: handleLogout,
    },
  ]

  useEffect(() => {
    const transformMenuItem = (menu: Menus): any => {
      let newMenu = undefined
      if (('roles' in menu && menu.roles && ref.current.hasAnyRoles(menu.roles)) ||
          ('permissions' in menu && menu.permissions && ref.current.hasAnyPermissions(menu.permissions)) ||
            menu.disabled || menu.hideInMenu) {
        newMenu = {
          ...menu,
          name: menu.labels[local as keyof typeof menu.labels],
          children: menu.children ? menu.children.map(transformMenuItem) : undefined,
        } as MenuDataItem
      }

      return newMenu
    }

    const findAllMenus = (menu: Menus) => {
      // Si le menu n'est pas désactivé, on l'ajoute à la liste des menus.
      if (!menu.disabled) {
        // Si le menu a des enfants, on explore les enfants.
        if ('children' in menu && menu.children) menu.children.forEach(findAllMenus)
        else {
          ref.current.dispatch(addMenu(menu))
          // Si le menu est celui du home, on l'ajoute à la liste des tags.
          if (menu.hideInMenu) ref.current.dispatch(addTag(menu))
        }
      }

    }


    const siderMenu: Menus[] = menuItems.map(transformMenuItem)
    // Ajouter les menus
    siderMenu.forEach(findAllMenus)

    setSidebarMenu(siderMenu)

  }, [local])

  useEffect(() => {
    if (!tablet) setSidebarVisible(true)
  }, [tablet])

  return (
    <>
      <NProgress isAnimating={isLoading} key={location.key} />
      <ProLayout
        logo={
          <Space>
            {tablet && (
              <Tooltip title={sidebarVisible ? "Hide sidebar" : "Show sidebar"}>
                <Button
                  type="text"
                  icon={sidebarVisible ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />}
                  onClick={() => setSidebarVisible(!sidebarVisible)}
                  size="large"
                />
              </Tooltip>
            )}
            <Logo
              color="blue"
              asLink
              href={PATH_GUEST.root}
              justify="center"
              gap="small"
              noTexte={tablet ? false : true}
              imgSize={{ h: 42, w: 42 }}
            />
          </Space>
        }
        title={false}
        layout="mix"
        fixedHeader
        fixSiderbar
        menuRender={sidebarVisible ? undefined : false}
        headerContentRender={() => (
          <Flex align="center" justify="space-between" style={{ flexGrow: 1, gap: 'small', marginLeft: '20px' }}>
            <AutoComplete
              popupMatchSelectWidth={250}
              style={{ width: 250 }}
              options={options}
              onSearch={handleSearch}
              size="middle"
              placeholder="search on menus"
              allowClear
            />
            <Flex align="center" gap="small">
              {/* <Tooltip title="Messages">
                <Button icon={<MessageOutlined />} type="text" size="large" />
              </Tooltip> */}
              <Tooltip title={local}>
                <Button icon={<Image preview={false} src={local === 'en' ? EnUSSvg: FrFRSvg} alt={local} />} onClick={() => toggleLocale()} type="text" size="large" />
              </Tooltip>
              <Tooltip title={theme}>
                <Button type="text" icon={themeIcon(theme)} onClick={() => updateTheme()} size="large" />
              </Tooltip>
              <Dropdown menu={{ items }}>
                <Flex align="center" className="cursor-pointer">
                  <Avatar
                    alt="user profile photo"
                    className="uppercase font-semibold"
                    style={{ backgroundColor: "#fde3cf", color: "#f56a00" }}
                  >
                    {user?.email?.[0] || 'U'}
                  </Avatar>
                </Flex>
              </Dropdown>
            </Flex>
          </Flex>
        )}
        pageTitleRender={(props, defaultTitle) => {
          document.title = import.meta.env.VITE_APP_TITLE
          return (props?.pathname === PATH_ADMIN.root)? "Home" : (defaultTitle ?? "Home")
        }}
        menuDataRender={() => sidebarMenu ?? [] }
        menuItemRender={(item, dom) => {
          if (item.path && !item.disabled) {
            return (
              <Link to={item.path as string} onClick={() => {
                // Handle menu item click, e.g., add tag
                // This could involve dispatching an action to add a tag
              }}>
                {dom}
              </Link>
            );
          }
          return dom;
        }}
        footerRender={() => (
          <TagsView className="fixed w-full bottom-0 right-0 z-[100] bg-[rgba(255,255,255,.9)] backdrop-blur-sm shadow-inner border-t" />
        )}
      >
        <TransitionGroup component={null}>
          <SwitchTransition>
            <CSSTransition
              key={`css-transition-${location.key}`}
              nodeRef={nodeRef}
              onEnter={() => {
                setIsLoading(true)
              }}
              onEntered={() => {
                setIsLoading(false)
              }}
              timeout={300}
              classNames="bottom-to-top"
              unmountOnExit
            >
              <div ref={nodeRef}>{children}</div>
            </CSSTransition>
          </SwitchTransition>
        </TransitionGroup>
        <div>
          <FloatButton.BackTop />
        </div>
      </ProLayout>
      {/* Footer is now handled by ProLayout's footerRender prop */}
    </>
  )
}

export default Dashboard
