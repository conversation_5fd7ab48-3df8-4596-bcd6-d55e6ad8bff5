import React, { useEffect, useRef } from "react";
import { Outlet, useLocation, useNavigate } from "react-router"
import { useAppAuth } from "~/hooks";
import { PATH_AUTH } from "~/constants";
import Dashboard from "./Dashboard"
import { Loading } from "~/components";

export const AdminLayout = ({children}: {children?: React.ReactNode}) => {
  const { isAuthenticated } = useAppAuth()
  const navigate = useNavigate()
  const loc = useLocation()
  const ref = useRef({ navigate, path: loc.pathname })

  useEffect(() => {
    if (!isAuthenticated) ref.current.navigate(PATH_AUTH.signin, { replace: true, state: { from: ref.current.path } })
  }, [isAuthenticated])

  if (!isAuthenticated) return <Loading />; //<Navigate to={PATH_AUTH.root} replace />;

  return (
    <Dashboard>
      {children ?? <Outlet />}
    </Dashboard>
  )
}
