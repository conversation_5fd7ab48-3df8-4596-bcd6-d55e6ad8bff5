import { ArrowLeftOutlined, CheckOutlined, DeleteOutlined, PlusOutlined, UploadOutlined } from "@ant-design/icons";
import { PageContainer } from "@ant-design/pro-components";
import {
    Button,
    Card,
    DatePicker,
    Form,
    Input,
    InputNumber,
    message,
    Select,
    Space,
    Steps,
    Table,
    Typography,
    Upload
} from "antd";
import type { RcFile } from "antd/es/upload";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router";
import * as XLSX from 'xlsx';
import { Agent } from "~/models/Agent";
import { FonctionsAgent, Periodicite, Prime, PrimeStatus, PrimeTypeMock, TypePrime, UniteVersement } from "~/models/Prime";
import { getAgents, updateAgent } from "~/services/api/agentService";
import { getCoefficientByFonctionAndPrimeType } from "~/services/api/fonctionService";
import { createPrime, getFonctionsApi, getTypesPrimesApi } from "~/services/api/primeService";
import IdentityManager from "~/services/IdentityManager";

const { Option } = Select;
const { TextArea } = Input;
const { Step } = Steps;

const AddRepartitionPage = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [uploading, setUploading] = useState(false);
  const [previewData, setPreviewData] = useState<Prime[]>([]);
  const [typesPrimes, setTypesPrimes] = useState<TypePrime[]|undefined>([]);
  const [selectedType, setSelectedType] = useState<PrimeTypeMock | undefined>();
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [unites, setUnites] = useState<UniteVersement[]>([]);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(false);
  const [primeData, setPrimeData] = useState<any>();
  const [fonctions, setFonctions] = useState<FonctionsAgent[]|undefined>([]);
  const [agentChanges, setAgentChanges] = useState<{[key: string]: {fonction: string, coefficient: number}}>({});
  const [validating, setValidating] = useState(false);

  // Charger les types de primes et les fonctions
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [primeTypesResponse, fonctionsResponse, agentsResponse] = await Promise.all([
          getTypesPrimesApi(),
          getFonctionsApi(),
          getAgents()
        ]);
        console.log("====================== primeTypesResponse ===============================")
        console.log(primeTypesResponse);
        console.log("====================== fonctionsResponse ===============================")
        console.log(fonctionsResponse);
        console.log("====================== agentsResponse ===============================")
        console.log(agentsResponse);

        setTypesPrimes(primeTypesResponse.data);
        setFonctions(fonctionsResponse.data);
        setAgents(agentsResponse.data);
      } catch (error) {
        message.error("Erreur lors du chargement des données");
      }
    };
    fetchData();
  }, []);

  const handleBack = () => {
    navigate("/primes");
  };

  // Fonction pour gérer le changement de fonction d'un agent
  const handleFonctionChange = (agentId: string, nouvelleFonction: string) => {
    if (!selectedType) return;

    // if (agents.some(agent => agent.id === agentId && agent.fonction === nouvelleFonction)) {
    //   // Si la fonction est remise à sa valeur initiale, supprimer les changements associés
    //   setAgentChanges(prev => {
    //     const { [agentId]: _, ...rest } = prev;
    //     return rest;
    //   });
    //   return;
    // }

    const nouveauCoefficient = getCoefficientByFonctionAndPrimeType(nouvelleFonction, selectedType.codeTypePrime as any);

    setAgentChanges(prev => ({
      ...prev,
      [agentId]: {
        fonction: nouvelleFonction,
        coefficient: nouveauCoefficient
      }
    }));

    // Mettre à jour l'agent dans la liste locale pour l'affichage
    setAgents(prev => prev.map(agent =>
      agent.id === agentId
        ? { ...agent, fonction: nouvelleFonction, coefficient: nouveauCoefficient }
        : agent
    ));
  };

  // Fonction pour valider les changements de fonction
  const handleValidateChanges = async () => {
    if (Object.keys(agentChanges).length === 0) {
      message.warning("Aucun changement à valider");
      return;
    }

    setValidating(true);
    try {
      // Mettre à jour chaque agent modifié
      const updatePromises = Object.entries(agentChanges).map(([agentId, changes]) =>
        updateAgent(agentId, {
          fonction: changes.fonction,
          coefficient: changes.coefficient
        })
      );

      await Promise.all(updatePromises);
      message.success(`${Object.keys(agentChanges).length} agent(s) mis à jour avec succès`);
      setAgentChanges({}); // Réinitialiser les changements
    } catch (error) {
      message.error("Erreur lors de la mise à jour des agents");
    } finally {
      setValidating(false);
    }
  };

  const onFinish = async (values: any) => {
    // Valider les données de la première étape
    if (!values.primeType || !values.periode ) {
      message.error("Veuillez remplir tous les champs obligatoires");
      return;
    }

    // Si avec_unite est false, vérifier si le montant est renseigné
    if (!selectedType?.avecUnite && !values.montant) {
      message.error("Veuillez renseigner le montant à répartir");
      return;
    }
    // Si avec_unite est true, vérifier que des unités ont été ajoutées
    if (selectedType?.avecUnite && unites.length === 0) {
      message.error("Veuillez ajouter au moins une unité");
      return;
    }

    // Stocker les données de la prime
    setPrimeData({
      name: values.name,
      type: values.primeType,
      amount: unites.reduce((sum, unite) => sum + unite.montant, 0) || 0,
      date: new Date().toISOString(),
      status: PrimeStatus.DRAFT,
      periodicite: values.periodicite,
      avec_unite: selectedType?.avec_unite,
      description: values.description,
      periode: values.periode,
      unites: unites
    })

    // Passer à l'étape suivante
    setCurrentStep(1);
  };

  const onFinish2 = async () => {
    try {
      if (Object.keys(agentChanges).length > 0) {
        message.warning("Veuillez valider les modifications avant de continuer");
        return;
      }
      setUploading(true);

      const response = await createPrime(primeData);
      message.success("Prime créée avec succès");
      // Rediriger vers la page de répartition des agents
      navigate(`/admin/primes/repartition/${response.id}`);
    } catch (err) {
      message.error("Erreur lors de la création de la prime");
      console.error(err);
    } finally {
      setUploading(false);
    }
  };

  const handleAddUnite = () => {
    const newUnite: UniteVersement = {
      id: IdentityManager.fetchUUID(),
      nom: '',
      montant: 0
    };
    setUnites([...unites, newUnite]);
  };

  const handleRemoveUnite = (id: string) => {
    setUnites(unites.filter(unite => unite.id !== id));
  };

  const handleUniteChange = (id: string, field: string, value: any) => {
    setUnites(unites.map(unite =>
      unite.id === id ? { ...unite, [field]: value } : unite
    ));
  };

  const handleImportUnites = (file: RcFile) => {
    setLoading(true);
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
        const jsonData = XLSX.utils.sheet_to_json(firstSheet);

        const importedUnites: UniteVersement[] = jsonData.map((row: any) => {
          const nom = (Object.values(row)[0] as string).toString() || '';
          const montant = parseFloat((Object.values(row)[1] as string).toString().replace(',', '.')) || 0;

          return {
            id: IdentityManager.fetchUUID(),
            nom,
            montant
          };
        });

        if (importedUnites.length > 0) {
          setUnites(importedUnites);
          message.success(`${importedUnites.length} unités importées avec succès`);
        } else {
          message.warning("Aucune unité trouvée dans le fichier");
        }
      } catch (error) {
        message.error("Erreur lors de l'importation du fichier");
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    reader.onerror = () => {
      message.error("Erreur lors de la lecture du fichier");
      setLoading(false);
    };

    reader.readAsArrayBuffer(file);
    return false;
  };

  const handleTypeChange = (value: string) => {
    // console.log("value", typesPrimes);
    setSelectedType(()=> typesPrimes?.find((type) => type.codeTypePrime === value) as PrimeTypeMock);
    setPreviewData([]);
    setUnites([]);
  };

  const unitesColumns = [
    {
      title: "Nom de l'unité",
      dataIndex: "nom",
      key: "nom",
      render: (text: string, record: UniteVersement) => (
        <Input
          value={text}
          onChange={(e) => handleUniteChange(record.id, 'nom', e.target.value)}
          placeholder="Nom de l'unité"
        />
      )
    },
    {
      title: "Montant",
      dataIndex: "montant",
      key: "montant",
      render: (value: number, record: UniteVersement) => (
        <InputNumber
          value={value}
          onChange={(value) => handleUniteChange(record.id, 'montant', value)}
          min={0}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: any, record: UniteVersement) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleRemoveUnite(record.id)}
        />
      )
    }
  ];

  const agentsColumns = [
    {
      title: "Matricule",
      dataIndex: "matricule",
      key: "matricule",
    },
    {
      title: "Nom",
      dataIndex: "nom",
      key: "nom",
    },
    {
      title: "Prénom",
      dataIndex: "prenom",
      key: "prenom",
    },
    {
      title: "Unité",
      dataIndex: "unite",
      key: "unite",
    },
    {
      title: "Grade",
      dataIndex: "grade",
      key: "grade",
    },
    {
      title: "Fonction",
      dataIndex: "fonction",
      key: "fonction",
      render: (fonction: string, record: Agent) => {
        return (
          <Select
            value={fonction}
            onChange={(value) => handleFonctionChange(record.id, value)}
            style={{ width: '100%' }}
            placeholder="Sélectionner une fonction"
          >
            {fonctions.map(f => (
              <Option key={f.code} value={f.code}>
                {f.libelle}
              </Option>
            ))}
          </Select>
        );
      }
    },
    {
      title: "Coefficient",
      dataIndex: "coefficient",
      key: "coefficient",
      render: (coef: number, record: Agent) => {
        const hasChanges = agentChanges[record.id];
        const displayCoef = hasChanges ? hasChanges.coefficient : coef;

        return (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <InputNumber
              value={displayCoef}
              min={0}
              step={0.1}
              style={{ width: '100px' }}
              disabled
            />
            {hasChanges && (
              <Typography.Text type="success" style={{ fontSize: '12px' }}>
                Modifié
              </Typography.Text>
            )}
          </div>
        );
      }
    }
  ];

  return (
    <PageContainer
      header={{
        title: (
          <Typography.Title level={3} className="w-full">
            {currentStep === 0 ? "Ajouter une Prime" : "Répartition des Agents"}
          </Typography.Title>
        ),
        onBack: handleBack,
        backIcon: <ArrowLeftOutlined />,
      }}
    >
      <Steps current={currentStep} style={{ marginBottom: 30 }}>
        <Steps.Step title="Informations de la Prime" />
        <Steps.Step title="Répartition des Agents" />
      </Steps>

      {currentStep === 0 ? (
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          style={{ maxWidth: 800, margin: "0 auto" }}
        >
          <Card title="Informations générales" style={{ marginBottom: 20 }}>
            <Form.Item
              name="primeType"
              label="Type de Prime"
              rules={[{ required: true, message: "Veuillez sélectionner un type" }]}
            >
              <Select
                placeholder="Sélectionnez un type de prime"
                onChange={handleTypeChange}
                style={{ width: "100%" }}
              >
                {typesPrimes?.map((type: any) => (
                  <Option key={type.codeTypePrime} value={type.codeTypePrime}>
                    {type.libelle}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            {selectedType && (
              <>
                <Form.Item
                  name="description"
                  label="Description"
                  initialValue={selectedType.description}
                >
                  <Input placeholder="Libellé de la prime" disabled />
                </Form.Item>

                <Form.Item
                  name="periodicite"
                  label="Périodicité"
                  initialValue={selectedType.periodicite}
                >
                  <Select placeholder="Sélectionnez une périodicité" disabled>
                    <Option value={Periodicite.ANNUELLE}>Annuelle</Option>
                    <Option value={Periodicite.TRIMESTRIELLE}>Trimestrielle</Option>
                    <Option value={Periodicite.MENSUELLE}>Mensuelle</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  name="periode"
                  label="Période"
                  rules={[{ required: true, message: "Veuillez saisir une période" }]}
                >
                  {selectedType.periodicite === Periodicite.ANNUELLE && (
                    <DatePicker picker="year" style={{ width: "100%" }} />
                  )}
                  {selectedType.periodicite === Periodicite.TRIMESTRIELLE && (
                    <Select placeholder="Sélectionnez un trimestre">
                      <Option value="T1">1er Trimestre</Option>
                      <Option value="T2">2ème Trimestre</Option>
                      <Option value="T3">3ème Trimestre</Option>
                      <Option value="T4">4ème Trimestre</Option>
                    </Select>
                  )}
                  {selectedType.periodicite === Periodicite.MENSUELLE && (
                    <DatePicker picker="month" style={{ width: "100%" }} />
                  )}
                </Form.Item>
              </>
            )}
          </Card>

          {selectedType && (selectedType.avec_unite ? (
            <Card title="Unités et Montants" style={{ marginBottom: 20 }}>
              <div style={{ marginBottom: 16 }}>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddUnite}
                  style={{ marginRight: 8 }}
                >
                  Ajouter une unité
                </Button>
                <Upload
                  accept=".xlsx,.xls"
                  showUploadList={false}
                  beforeUpload={handleImportUnites}
                >
                  <Button icon={<UploadOutlined />} loading={loading}>
                    Importer depuis Excel
                  </Button>
                </Upload>
              </div>

              <Table
                dataSource={unites}
                columns={unitesColumns}
                rowKey="id"
                pagination={false}
                locale={{ emptyText: "Aucune unité ajoutée" }}
              />

              {unites.length > 0 && (
                <div style={{ marginTop: 16, textAlign: 'right' }}>
                  <Typography.Text strong>
                    Total: {unites.reduce((sum, unite) => sum + unite.montant, 0).toLocaleString()} FCFA
                  </Typography.Text>
                </div>
              )}
            </Card>):
            (<Card title="Montant à répartir" style={{ marginBottom: 20 }}>
              <Form.Item
                  name="montant"
                  label="Montant"
                  rules={[{ required: true, message: "Veuillez saisir un montant" }]}
                >
                  <InputNumber
                    min={0}
                    style={{ width: "100%" }}
                  />
                </Form.Item>

            </Card>
          ))}

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={uploading}
              >
                Suivant
              </Button>
              <Button onClick={handleBack}>Annuler</Button>
            </Space>
          </Form.Item>
        </Form>
      ) : (
        <Card title="Liste des Agents" style={{ marginBottom: 20 }}>
          {Object.keys(agentChanges).length > 0 && (
            <div style={{ marginBottom: 16, padding: 16, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 6 }}>
              <Typography.Text strong style={{ color: '#52c41a' }}>
                {Object.keys(agentChanges).length} agent(s) modifié(s)
              </Typography.Text>
              <Button
                type="primary"
                icon={<CheckOutlined />}
                onClick={handleValidateChanges}
                loading={validating}
                style={{ marginLeft: 16 }}
                size="small"
              >
                Valider les changements
              </Button>
            </div>
          )}

          <Table
            dataSource={agents}
            columns={agentsColumns}
            rowKey="id"
            pagination={{ pageSize: 10 }}
            locale={{ emptyText: "Aucun agent trouvé" }}
          />

          <div style={{ marginTop: 20 }}>
            <Space>
              <Button
                type="primary"
                onClick={onFinish2}
                loading={uploading}
              >
                Enregistrer la répartition
              </Button>
              <Button onClick={() => setCurrentStep(0)}>Retour</Button>
            </Space>
          </div>
        </Card>
      )}
    </PageContainer>
  );
};

export default AddRepartitionPage;
