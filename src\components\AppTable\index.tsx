import React from 'react';
import { ProTable, ProTableProps, ProColumns } from '@ant-design/pro-components';
import { Button, Space, Tag, Typography, theme, Tooltip } from 'antd';
import { createStyles } from 'antd-style';
import {
  ReloadOutlined,
  DownloadOutlined,
  FilterOutlined,
  SettingOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { ReactNode } from 'react';

const { Text } = Typography;

// Styles personnalisés pour AppTable
const useStyles = createStyles(({ token, css }) => ({
  appTable: css`
    .dgd-pro-table-list-toolbar {
      // background: linear-gradient(135deg, ${token.colorPrimary}08 0%, ${token.colorPrimary}04 100%);
      border-radius: ${token.borderRadius}px ${token.borderRadius}px 0 0;
      border-bottom: 1px solid ${token.colorBorder};
      padding: ${token.paddingMD}px ${token.paddingLG}px;
    }

    .dgd-pro-table-list-toolbar-title {
      font-weight: 600;
      color: ${token.colorText};
      display: flex;
      align-items: center;
      gap: ${token.marginSM}px;

      .anticon {
        color: ${token.colorPrimary};
      }
    }

    .dgd-table-thead > tr > th {
      background: ${token.colorFillAlter};
      font-weight: 600;
      color: ${token.colorText};
      border-bottom: 2px solid ${token.colorPrimary};
    }

    .dgd-table-tbody > tr:hover > td {
      background: ${token.colorPrimaryBg};
    }

    .dgd-table-tbody > tr > td {
      transition: all 0.2s ease;
    }

    .dgd-pagination {
      margin-top: ${token.marginLG}px;
      padding: ${token.paddingMD}px;
      // background: ${token.colorFillQuaternary};
      // border-radius: 0 0 ${token.borderRadius}px ${token.borderRadius}px;
    }

    &.app-table-compact {
      .dgd-table-tbody > tr > td {
        padding: ${token.paddingSM}px ${token.paddingMD}px;
      }
    }

    &.app-table-elevated {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      border-radius: ${token.borderRadius}px;
      overflow: hidden;
    }
  `,

  tableActions: css`
    display: flex;
    gap: ${token.marginXS}px;

    .dgd-btn {
      border-radius: ${token.borderRadius}px;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  `,

  statusTag: css`
    border-radius: ${token.borderRadius}px;
    font-weight: 500;
    padding: 2px 8px;
  `,

  tableToolbar: css`
    display: flex;
    align-items: center;
    gap: ${token.marginSM}px;

    .toolbar-item {
      display: flex;
      align-items: center;
      gap: ${token.marginXS}px;
    }
  `
}));

export interface AppTableProps<T = any> extends ProTableProps<T, any> {
  /** Titre du tableau avec icône optionnelle */
  title?: ReactNode;
  /** Icône pour le titre */
  titleIcon?: ReactNode;
  /** Description sous le titre */
  description?: ReactNode;
  /** Variante de style */
  variant?: 'default' | 'compact' | 'elevated';
  /** Afficher les actions par défaut (refresh, export, etc.) */
  showDefaultActions?: boolean;
  /** Actions personnalisées dans la toolbar */
  customActions?: ReactNode[];
  /** Afficher le compteur de résultats */
  showResultCount?: boolean;
  /** Texte personnalisé pour le compteur */
  resultCountText?: (total: number, range: [number, number]) => string;
  /** Couleur d'accent pour le header */
  accentColor?: string;
}

const AppTable = <T extends Record<string, any>>({
  title,
  titleIcon,
  description,
  variant = 'default',
  showDefaultActions = true,
  customActions = [],
  showResultCount = true,
  resultCountText,
  accentColor,
  className,
  toolBarRender,
  headerTitle,
  pagination,
  ...props
}: AppTableProps<T>) => {
  const { styles, cx } = useStyles();
  const { token } = theme.useToken();

  // Classes CSS
  const tableClasses = cx(
    styles.appTable,
    {
      'app-table-compact': variant === 'compact',
      'app-table-elevated': variant === 'elevated',
    },
    className
  );

  // Actions par défaut
  const defaultActions = showDefaultActions ? [
    <Tooltip key="refresh" title="Actualiser">
      <Button
        icon={<ReloadOutlined />}
        onClick={() => props.actionRef?.current?.reload?.()}
      >
        Actualiser
      </Button>
    </Tooltip>,
    <Tooltip key="export" title="Exporter">
      <Button icon={<DownloadOutlined />}>
        Exporter
      </Button>
    </Tooltip>,
    <Tooltip key="settings" title="Paramètres">
      <Button icon={<SettingOutlined />} />
    </Tooltip>
  ] : [];

  // Rendu du titre personnalisé
  const renderTitle = () => {
    if (!title && !headerTitle) return undefined;

    return (
      <div
        style={{
          borderLeft: accentColor ? `4px solid ${accentColor}` : undefined,
          paddingLeft: accentColor ? token.paddingMD : undefined,
        }}
      >
        <Space>
          {titleIcon}
          <span>{title || headerTitle}</span>
        </Space>
        {description && (
          <div style={{ marginTop: token.marginXS }}>
            <Text type="secondary" style={{ fontSize: token.fontSizeSM }}>
              {description}
            </Text>
          </div>
        )}
      </div>
    );
  };

  // Rendu de la toolbar
  const renderToolbar = (action: any, rows: any) => {
    const defaultToolbar = toolBarRender ? toolBarRender(action, rows) : [];

    return [
      ...defaultToolbar,
      ...customActions,
      ...defaultActions
    ];
  };

  // Configuration de la pagination
  const paginationConfig = {
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: resultCountText || ((total: number, range: [number, number]) =>
      `${range[0]}-${range[1]} sur ${total} éléments`
    ),
    ...pagination
  };

  return (
    <ProTable<T>
      {...props}
      className={tableClasses}
      headerTitle={renderTitle()}
      toolBarRender={renderToolbar}
      pagination={showResultCount ? paginationConfig : pagination}
      search={false}
      options={{
        setting: {
          listsHeight: 400,
        },
        ...props.options
      }}
      scroll={{ x: 800, ...props.scroll }}
      size={variant === 'compact' ? 'small' : 'middle'}
    />
  );
};

// Composants utilitaires pour les colonnes
export const AppTableActions = ({ children }: { children: ReactNode }) => {
  const { styles } = useStyles();
  return <div className={styles.tableActions}>{children}</div>;
};

export const AppTableStatus = ({
  status,
  color,
  text
}: {
  status: string;
  color?: string;
  text?: string;
}) => {
  const { styles } = useStyles();
  return (
    <Tag color={color} className={styles.statusTag}>
      {text || status}
    </Tag>
  );
};

export default AppTable;
