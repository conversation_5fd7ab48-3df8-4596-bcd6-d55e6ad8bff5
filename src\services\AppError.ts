import { message } from "antd";

/**
 * Custom App Error
 */
export default class AppError extends Error {
  data;
  status;

  constructor(message:string, data:any = {}, status:number = NaN) {
    super(message);
    this.data = data;
    this.status = status || data.response?.status || (data.data?.status?.split(" ")[0]*1);
  }

  static log(error: any, message: string = "") {
    if (import.meta.env.DEV) console.error("CLIENT MESSAGE: %s \nREQUEST MESSAGE: %s \nSERVER MESSAGE: %s \nERROR: %s", message, error.message, error.data?.error, JSON.stringify(error));
    else console.error("CLIENT MESSAGE: %s \nREQUEST MESSAGE: %s \nSERVER MESSAGE: %s", message, error.message, error.data?.error);
  }

  static throw(error: any, message?: string, status: number = NaN) {
    AppError.log(error, message);
    throw new AppError((message || error.data?.error || error.message), error.data, status || error.status);
  }

  static message(error: any, msg: string = "", response?: any): any {
    AppError.log(error, msg);
    message.error((msg || error.data?.error || error.message));
    if (import.meta.env.DEV && msg) message.error((error.data?.error || error.message));
    return response ?? error.data;
  }
}
