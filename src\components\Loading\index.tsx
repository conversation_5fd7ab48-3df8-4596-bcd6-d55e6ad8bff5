import { css, cx } from "~/themes"
import { LogoDouane } from "../LogoDouane.svg"

export const Loading = ({noCss}: {noCss?: boolean}) => {
  return (
    <div className={noCss ? '' : style}>
      <div className="relative">
        <LogoDouane height={27} width={27} />
        <div className="loader left-[-7px]">
          <div></div>
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
    </div>
  )
}

const style = cx(
  css({
    width: "100%",
    height: "calc(100vh - var(--dgd-head-height))",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    // "& .loader": {
    //   left: -7,
    // },
  })
)
