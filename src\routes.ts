import React from "react"
import ErrorPage from "./pages/ErrorPage"
import { createBrowserRouter, Navigate, RouteObject } from "react-router"
import { BASENAME, HOME, PATH_ADMIN, PATH_AUTH, PATH_GUEST, REQUEST, SIGNIN, UPDATE_PASSWORD } from "./constants"
import { loadPage } from "./hocs"
import { Loading } from "./components"
import { AdminLayout, AuthLayout, GuestLayout } from "./layouts"
import { PRIMES, repartitionsListLoader, repartitionsNewLoader } from "./pages/admin/dafl/primes"


// Routes publiques
const publicRoutes: RouteObject[] = [
  {
    index: true,
    Component: loadPage(import("./pages/home/<USER>")),
  },
  {
    path: HOME,
    Component: loadPage(import("./pages/home/<USER>")),
  },
]

// Routes d'authentification
const authRoutes: RouteObject[] = [
  {
    index: true,
    element: React.createElement(Navigate, {to: PATH_AUTH.signin, replace: true}),
  },
  {
    path: SIGNIN,
    Component: loadPage(import("./pages/auth/SignInPage")),
  },
  {
    path: REQUEST,
    Component: loadPage(import("./pages/auth/RequestPage")),
  },
  {
    path: UPDATE_PASSWORD,
    Component: loadPage(import("./pages/auth/UpdatePasswordPage")),
  },
]

// Routes protégées (admin)
const protectedRoutes: RouteObject[] = [

  {
    index:true,
    Component: loadPage(import("./pages/admin/AdminPage"), {withFooter: true}),
  },
  {
    path: PATH_ADMIN.convoyage,
    Component: loadPage(import("./pages/dev/convoyage/ConvoyageAdminPage"), {requiredRoles:['admin']}, true),
  },
  {
    path: PATH_ADMIN.utilisateurs,
    Component: loadPage(import("./pages/dev/utilisateurs/UserPage"), {requiredRoles:['admin']}, true),
  },
  {
    path: PATH_ADMIN.agents,
    Component: loadPage(import("./pages/dev/agents"), {requiredRoles:['admin']}, true),
  },
  {
    path: PATH_ADMIN.dafl,
    children: [
      {
        index: true,
        Component: ErrorPage,
      },
      {
        path: PRIMES,
        children: [
          {
            index: true,
            Component: loadPage(import("./pages/admin/dafl/primes/PrimePage"), undefined),
          },
          {
            path: "repartitions",
            Component: loadPage(import("./pages/admin/dafl/primes/repartition/RepartitionsPage"), undefined),
            loader: repartitionsListLoader,
          },
          // {
          //   path: "repartitions/:id",
          //   Component: loadPage(import("./pages/admin/dafl/primes/repartition/RepartitionsPage"), undefined),
          // },
          {
            path: "repartitions/add",
            Component: loadPage(import("./pages/admin/dafl/primes/repartition/AddRepartitionPage"), undefined),
            loader: repartitionsNewLoader,
          },
          // {
          //   path: "edit/:id",
          //   Component: loadPage(import("./pages/admin/dafl/primes/edit/[id]"), undefined),
          // },
          // {
          //   path: "typePrime",
          //   Component: loadPage(import("./pages/admin/dafl/primes/typePrime"), undefined),
          //   loader: typesPrimesLoader,
          // },
          // {
          //   path: "management",
          //   Component: loadPage(import("./pages/admin/dafl/primes/management/PrimeManagementPage"), undefined),
          // },
          // {
          //   path: "coefficients",
          //   Component: loadPage(import("./pages/admin/dafl/primes/coefficients/PrimeSelectionPage"), undefined),
          // },
          // {
          //   path: "coefficients/:primeId",
          //   Component: loadPage(import("./pages/admin/dafl/primes/coefficients/CoefficientManagementPage"), undefined),
          // },
        ]
      },
    ],
  },
]

// Création du routeur
const router = createBrowserRouter(
  [
    {
      HydrateFallback: Loading,
      ErrorBoundary: ErrorPage,
      children: [
        {
          Component: GuestLayout,
          children: [
            ...publicRoutes,
          ]
        },
        {
          path: PATH_AUTH.root,
          Component: AuthLayout,
          children: [
            ...authRoutes,
          ]
        },
        {
          path: PATH_ADMIN.root,
          Component: AdminLayout,
          children: [
            {
              HydrateFallback: Loading,
              ErrorBoundary: ErrorPage,
              children: [
                ...protectedRoutes,
              ]
            },
          ]
        },
        {
          path: PATH_GUEST.login,
          element: React.createElement(Navigate, {to: PATH_AUTH.signin}),
        },
        {// Routes d'erreurs
          path: '/:id',
          Component: ErrorPage
        },
      ],
    }
  ],
  { basename: BASENAME }
)

export default router
