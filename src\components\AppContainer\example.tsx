import React, { useState } from 'react';
import { Button, Card, Space, Typography, Row, Col } from 'antd';
import { 
  UserOutlined, 
  PlusOutlined, 
  SettingOutlined, 
  QuestionCircleOutlined,
  ExportOutlined,
  ImportOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import AppContainer from './index';
import { AppCard } from '../AppCard';

const { Title, Paragraph } = Typography;

/**
 * Exemple d'utilisation d'AppContainer avec toutes ses fonctionnalités
 */
const AppContainerExample: React.FC = () => {
  const [fullscreen, setFullscreen] = useState(false);
  const [variant, setVariant] = useState<'default' | 'glass' | 'gradient' | 'minimal' | 'fullscreen'>('default');

  const handleBack = () => {
    console.log('Retour cliqué');
  };

  const breadcrumbItems = [
    { title: 'Accueil', href: '/' },
    { title: 'Administration', href: '/admin' },
    { title: 'Utilisateurs', href: '/admin/users' },
    { title: 'Gestion' }
  ];

  const headerActions = [
    <Button key="export" icon={<ExportOutlined />}>
      Exporter
    </Button>,
    <Button key="import" icon={<ImportOutlined />}>
      Importer
    </Button>,
    <Button key="refresh" icon={<ReloadOutlined />} type="primary">
      Actualiser
    </Button>
  ];

  const floatingActions = [
    <Button
      key="add"
      type="primary"
      shape="circle"
      icon={<PlusOutlined />}
      size="large"
      title="Ajouter un utilisateur"
    />,
    <Button
      key="settings"
      type="default"
      shape="circle"
      icon={<SettingOutlined />}
      size="large"
      title="Paramètres"
    />,
    <Button
      key="help"
      type="default"
      shape="circle"
      icon={<QuestionCircleOutlined />}
      size="large"
      title="Aide"
    />
  ];

  return (
    <div style={{ padding: '20px' }}>
      {/* Contrôles pour tester les variantes */}
      <Card style={{ marginBottom: '20px' }}>
        <Title level={4}>Test des variantes d'AppContainer</Title>
        <Space wrap>
          <Button 
            type={variant === 'default' ? 'primary' : 'default'}
            onClick={() => setVariant('default')}
          >
            Default
          </Button>
          <Button 
            type={variant === 'glass' ? 'primary' : 'default'}
            onClick={() => setVariant('glass')}
          >
            Glass
          </Button>
          <Button 
            type={variant === 'gradient' ? 'primary' : 'default'}
            onClick={() => setVariant('gradient')}
          >
            Gradient
          </Button>
          <Button 
            type={variant === 'minimal' ? 'primary' : 'default'}
            onClick={() => setVariant('minimal')}
          >
            Minimal
          </Button>
          <Button 
            type={variant === 'fullscreen' ? 'primary' : 'default'}
            onClick={() => setVariant('fullscreen')}
          >
            Fullscreen
          </Button>
        </Space>
      </Card>

      {/* AppContainer avec toutes les fonctionnalités */}
      <AppContainer
        title="Gestion des Utilisateurs"
        subtitle="Interface complète de gestion des comptes utilisateurs avec toutes les fonctionnalités avancées"
        icon={<UserOutlined />}
        variant={variant}
        accentColor="#52c41a"
        showBackButton={true}
        backButtonText="Retour au tableau de bord"
        onBack={handleBack}
        breadcrumbItems={breadcrumbItems}
        headerActions={headerActions}
        floatingActions={floatingActions}
        fullscreen={fullscreen}
        onFullscreenToggle={setFullscreen}
      >
        {/* Contenu de la page */}
        <Row gutter={[16, 16]}>
          <Col xs={24} md={8}>
            <AppCard
              title="Statistiques"
              icon={<UserOutlined />}
              variant="elevated"
              accentColor="#1890ff"
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Title level={2} style={{ margin: 0, color: '#1890ff' }}>1,234</Title>
                  <Paragraph type="secondary">Utilisateurs actifs</Paragraph>
                </div>
                <div>
                  <Title level={2} style={{ margin: 0, color: '#52c41a' }}>98.5%</Title>
                  <Paragraph type="secondary">Taux de satisfaction</Paragraph>
                </div>
              </Space>
            </AppCard>
          </Col>
          
          <Col xs={24} md={8}>
            <AppCard
              title="Actions rapides"
              variant="gradient"
              accentColor="#722ed1"
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button type="primary" block icon={<PlusOutlined />}>
                  Nouvel utilisateur
                </Button>
                <Button block icon={<ExportOutlined />}>
                  Exporter la liste
                </Button>
                <Button block icon={<SettingOutlined />}>
                  Paramètres
                </Button>
              </Space>
            </AppCard>
          </Col>
          
          <Col xs={24} md={8}>
            <AppCard
              title="Notifications"
              variant="glass"
              accentColor="#fa8c16"
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <Paragraph>
                  <strong>3 nouveaux utilisateurs</strong> en attente de validation
                </Paragraph>
                <Paragraph>
                  <strong>Maintenance programmée</strong> ce weekend
                </Paragraph>
                <Paragraph>
                  <strong>Mise à jour</strong> disponible
                </Paragraph>
              </Space>
            </AppCard>
          </Col>
        </Row>

        <Row style={{ marginTop: '24px' }}>
          <Col span={24}>
            <AppCard
              title="Contenu principal"
              description="Ici se trouve le contenu principal de votre page"
              variant="elevated"
            >
              <Paragraph>
                Cet exemple montre toutes les fonctionnalités d'AppContainer :
              </Paragraph>
              <ul>
                <li><strong>Header personnalisé</strong> avec titre, sous-titre et icône</li>
                <li><strong>Bouton retour</strong> configurable</li>
                <li><strong>Breadcrumb</strong> automatique</li>
                <li><strong>Actions dans le header</strong> (Export, Import, Actualiser)</li>
                <li><strong>Actions flottantes</strong> (Ajouter, Paramètres, Aide)</li>
                <li><strong>Variantes de style</strong> (Default, Glass, Gradient, Minimal, Fullscreen)</li>
                <li><strong>Couleur d'accent</strong> personnalisable</li>
                <li><strong>Mode plein écran</strong> avec toggle</li>
              </ul>
              
              <Paragraph>
                Testez les différentes variantes avec les boutons en haut de la page !
              </Paragraph>
            </AppCard>
          </Col>
        </Row>
      </AppContainer>
    </div>
  );
};

export default AppContainerExample;
